import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../../../../core/config/responsive.dart';
import '../../../../core/enum/user_role.dart';
import '../../../cubit/top_performers/top_performers_cubit.dart';
import '../../../cubit/user/user_cubit.dart';
import 'month_year_picker.dart';

class CalendarPickerService {
  static void showMonthYearPicker({
    required BuildContext anchorContext,
    required ValueNotifier selectMonth,
    required TopPerformersCubit topPerformersCubit,
    required BuildContext parentContext,
  }) {
    final DateTime initialDate = DateTime.parse('${selectMonth.value}-01');
    final bool isMobile = Responsive.isMobile(parentContext);
    final bool isTablet = Responsive.isTablet(parentContext);

    if (isMobile || isTablet) {
      _showResponsiveDialog(
        parentContext: parentContext,
        initialDate: initialDate,
        selectMonth: selectMonth,
        topPerformersCubit: topPerformersCubit,
      );
    } else {
      _showOverlayPicker(
        anchorContext: anchorContext,
        parentContext: parentContext,
        initialDate: initialDate,
        selectMonth: selectMonth,
        topPerformersCubit: topPerformersCubit,
      );
    }
  }

  static void _showResponsiveDialog({
    required BuildContext parentContext,
    required DateTime initialDate,
    required ValueNotifier selectMonth,
    required TopPerformersCubit topPerformersCubit,
  }) {
    final bool isMobile = Responsive.isMobile(parentContext);
    showDialog(
      context: parentContext,
      barrierDismissible: true,
      barrierColor: Colors.black.withValues(alpha: 0.3),
      builder: (BuildContext dialogContext) {
        return Dialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          insetPadding: EdgeInsets.all(isMobile ? 16 : 24),
          child: Center(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(dialogContext).size.width - 32,
                maxHeight: MediaQuery.of(dialogContext).size.height - 100,
              ),
              child: MonthYearPicker(
                anchorKey: GlobalKey(),
                initialDate: initialDate,
                onDateSelected: (DateTime date) async {
                  await _handleDateSelection(
                    date: date,
                    selectMonth: selectMonth,
                    topPerformersCubit: topPerformersCubit,
                    parentContext: parentContext,
                  );
                  if (dialogContext.mounted) {
                    Navigator.of(dialogContext).pop();
                  }
                },
                onCancel: () {
                  if (dialogContext.mounted) {
                    Navigator.of(dialogContext).pop();
                  }
                },
              ),
            ),
          ),
        );
      },
    );
  }

  static void _showOverlayPicker({
    required BuildContext anchorContext,
    required BuildContext parentContext,
    required DateTime initialDate,
    required ValueNotifier selectMonth,
    required TopPerformersCubit topPerformersCubit,
  }) {
    OverlayEntry? entry;

    entry = OverlayEntry(
      builder: (context) => _ResponsiveCalendarOverlay(
        anchorContext: anchorContext,
        parentContext: parentContext,
        initialDate: initialDate,
        selectMonth: selectMonth,
        topPerformersCubit: topPerformersCubit,
        onClose: () => entry?.remove(),
      ),
    );

    final overlay = Overlay.of(parentContext);
    overlay.insert(entry);
  }

  static Future _handleDateSelection({
    required DateTime date,
    required ValueNotifier selectMonth,
    required TopPerformersCubit topPerformersCubit,
    required BuildContext parentContext,
  }) async {
    final formattedDate = DateFormat('yyyy-MM').format(date);
    selectMonth.value = formattedDate;

    if (!parentContext.mounted) return;

    final userRole = parentContext.read<UserCubit>().state.user?.role;

    if (userRole == UserRole.agent || userRole == UserRole.brokerage) {
      await topPerformersCubit.getAgentTopPerformers(formattedDate);
    } else if (userRole == UserRole.admin ||
        userRole == UserRole.platformOwner) {
      await topPerformersCubit.getBrokerageTopPerformers(formattedDate);
      await topPerformersCubit.getAgentTopPerformers(formattedDate);
    }
  }
}

class _ResponsiveCalendarOverlay extends StatefulWidget {
  final BuildContext anchorContext;
  final BuildContext parentContext;
  final DateTime initialDate;
  final ValueNotifier selectMonth;
  final TopPerformersCubit topPerformersCubit;
  final VoidCallback onClose;

  const _ResponsiveCalendarOverlay({
    required this.anchorContext,
    required this.parentContext,
    required this.initialDate,
    required this.selectMonth,
    required this.topPerformersCubit,
    required this.onClose,
  });

  @override
  State<_ResponsiveCalendarOverlay> createState() =>
      _ResponsiveCalendarOverlayState();
}

class _ResponsiveCalendarOverlayState extends State<_ResponsiveCalendarOverlay>
    with WidgetsBindingObserver {
  PickerPosition? _currentPosition;
  double? _pickerWidth;
  double? _pickerHeight;
  Size? _lastScreenSize;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updatePosition();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    if (mounted) {
      _updatePosition();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _updatePosition();
      }
    });
  }

  void _updatePosition() {
    if (!mounted) return;

    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final screenHeight = mediaQuery.size.height;

    final padding = mediaQuery.padding.left > 0
        ? mediaQuery.padding.left
        : 16.0;

    final pickerWidth = (screenWidth * 0.25).clamp(200.0, 350.0);
    final pickerHeight = (screenHeight * 0.5).clamp(300.0, 400.0);

    final currentScreenSize = Size(screenWidth, screenHeight);
    _lastScreenSize = currentScreenSize;

    RenderBox? renderBox;
    try {
      renderBox = widget.anchorContext.findRenderObject() as RenderBox?;
    } catch (e) {
      _setDefaultPosition(
        screenWidth,
        screenHeight,
        pickerWidth,
        pickerHeight,
        padding,
      );
      return;
    }

    if (renderBox == null || !renderBox.hasSize) {
      _setDefaultPosition(
        screenWidth,
        screenHeight,
        pickerWidth,
        pickerHeight,
        padding,
      );
      return;
    }

    final Offset offset = renderBox.localToGlobal(Offset.zero);
    final Size size = renderBox.size;

    final newPosition = _calculateOptimalPosition(
      anchorOffset: offset,
      anchorSize: size,
      screenWidth: screenWidth,
      screenHeight: screenHeight,
      pickerWidth: pickerWidth,
      pickerHeight: pickerHeight,
      padding: padding,
      anchorGap: 8.0,
    );

    if (mounted) {
      setState(() {
        _currentPosition = newPosition;
        _pickerWidth = pickerWidth;
        _pickerHeight = pickerHeight;
      });
    }
  }

  void _setDefaultPosition(
    double screenWidth,
    double screenHeight,
    double pickerWidth,
    double pickerHeight,
    double padding,
  ) {
    double left;
    if (screenWidth - pickerWidth - padding >= padding) {
      left = screenWidth - pickerWidth - padding;
    } else {
      left = (screenWidth - pickerWidth) / 2;
      left = left.clamp(padding, screenWidth - pickerWidth - padding);
    }
    final top = (screenHeight - pickerHeight) / 2;
    if (mounted) {
      setState(() {
        _currentPosition = PickerPosition(left: left, top: top);
        _pickerWidth = pickerWidth;
        _pickerHeight = pickerHeight;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (_currentPosition == null) {
          return const SizedBox.shrink();
        }
        return NotificationListener<SizeChangedLayoutNotification>(
          onNotification: (notification) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                _updatePosition();
              }
            });
            return true;
          },
          child: MediaQuery(
            data: MediaQuery.of(context),
            child: LayoutBuilder(
              builder: (context, constraints) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (mounted) {
                    final currentMediaQuery = MediaQuery.of(context);
                    if (_lastScreenSize != null &&
                        (_lastScreenSize!.width !=
                                currentMediaQuery.size.width ||
                            _lastScreenSize!.height !=
                                currentMediaQuery.size.height)) {
                      _updatePosition();
                    }
                  }
                });

                return Material(
                  key: UniqueKey(),
                  type: MaterialType.transparency,
                  child: Stack(
                    children: [
                      Positioned.fill(
                        child: GestureDetector(
                          onTap: widget.onClose,
                          child: Container(
                            color: Colors.black.withValues(alpha: 0.3),
                          ),
                        ),
                      ),
                      AnimatedPositioned(
                        duration: const Duration(milliseconds: 200),
                        curve: Curves.easeInOut,
                        left: _currentPosition!.left,
                        top: _currentPosition!.top,
                        child: Material(
                          elevation: 8.0,
                          borderRadius: BorderRadius.circular(8.0),
                          child: SizedBox(
                            width: _pickerWidth,
                            height: _pickerHeight,
                            child: MonthYearPicker(
                              anchorKey: GlobalKey(),
                              initialDate: widget.initialDate,
                              onDateSelected: (DateTime date) async {
                                await CalendarPickerService._handleDateSelection(
                                  date: date,
                                  selectMonth: widget.selectMonth,
                                  topPerformersCubit: widget.topPerformersCubit,
                                  parentContext: widget.parentContext,
                                );
                                widget.onClose();
                              },
                              onCancel: widget.onClose,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  PickerPosition _calculateOptimalPosition({
    required Offset anchorOffset,
    required Size anchorSize,
    required double screenWidth,
    required double screenHeight,
    required double pickerWidth,
    required double pickerHeight,
    required double padding,
    required double anchorGap,
  }) {
    // Always recalculate top from anchor offset
    double top = anchorOffset.dy + anchorSize.height + anchorGap;

    // If picker would overflow bottom, move it above the anchor
    final double bottomOverflow =
        (top + pickerHeight) - (screenHeight - padding);
    if (bottomOverflow > 0) {
      final double abovePosition = anchorOffset.dy - pickerHeight - anchorGap;
      if (abovePosition >= padding) {
        top = abovePosition;
      } else {
        top = (screenHeight - pickerHeight - padding).clamp(
          padding,
          screenHeight - pickerHeight - padding,
        );
      }
    }

    // Clamp to avoid going off screen
    top = top.clamp(padding, screenHeight - pickerHeight - padding);

    // LEFT positioning logic (unchanged)
    final double minLeft = padding;
    final double maxLeft = screenWidth - pickerWidth - padding;
    final double anchorCenterX = anchorOffset.dx + (anchorSize.width / 2);
    final double anchorRightEdge = anchorOffset.dx + anchorSize.width;

    double left;
    if (maxLeft < minLeft) {
      left = (screenWidth - pickerWidth) / 2;
    } else {
      final double preferredLeft = anchorRightEdge - pickerWidth;
      if (preferredLeft >= minLeft && preferredLeft <= maxLeft) {
        left = preferredLeft;
      } else if (anchorOffset.dx >= minLeft && anchorOffset.dx <= maxLeft) {
        left = anchorOffset.dx;
      } else if (screenWidth - pickerWidth - padding >= minLeft) {
        left = screenWidth - pickerWidth - padding;
      } else {
        left = anchorCenterX - (pickerWidth / 2);
      }
    }
    left = left.clamp(minLeft, maxLeft);

    return PickerPosition(left: left, top: top);
  }
}

class PickerPosition {
  final double left;
  final double top;
  const PickerPosition({required this.left, required this.top});
}
