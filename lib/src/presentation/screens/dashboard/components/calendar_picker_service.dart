import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../../../../core/config/responsive.dart';
import '../../../../core/enum/user_role.dart';
import '../../../cubit/top_performers/top_performers_cubit.dart';
import '../../../cubit/user/user_cubit.dart';
import 'month_year_picker.dart';

class CalendarPickerService {
  static void showMonthYearPicker({
    required BuildContext anchorContext,
    required ValueNotifier selectMonth,
    required TopPerformersCubit topPerformersCubit,
    required BuildContext parentContext,
  }) {
    final DateTime initialDate = DateTime.parse('${selectMonth.value}-01');
    final bool isMobile = Responsive.isMobile(parentContext);
    final bool isTablet = Responsive.isTablet(parentContext);

    if (isMobile || isTablet) {
      _showResponsiveDialog(
        parentContext: parentContext,
        initialDate: initialDate,
        selectMonth: selectMonth,
        topPerformersCubit: topPerformersCubit,
      );
    } else {
      _showOverlayPicker(
        anchorContext: anchorContext,
        parentContext: parentContext,
        initialDate: initialDate,
        selectMonth: selectMonth,
        topPerformersCubit: topPerformersCubit,
      );
    }
  }

  static void _showResponsiveDialog({
    required BuildContext parentContext,
    required DateTime initialDate,
    required ValueNotifier selectMonth,
    required TopPerformersCubit topPerformersCubit,
  }) {
    final bool isMobile = Responsive.isMobile(parentContext);
    showDialog(
      context: parentContext,
      barrierDismissible: true,
      barrierColor: Colors.black.withValues(alpha: 0.3),
      builder: (BuildContext dialogContext) {
        return Dialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          insetPadding: EdgeInsets.all(isMobile ? 16 : 24),
          child: Center(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(dialogContext).size.width - 32,
                maxHeight: MediaQuery.of(dialogContext).size.height - 100,
              ),
              child: MonthYearPicker(
                anchorKey: GlobalKey(),
                initialDate: initialDate,
                onDateSelected: (DateTime date) async {
                  await _handleDateSelection(
                    date: date,
                    selectMonth: selectMonth,
                    topPerformersCubit: topPerformersCubit,
                    parentContext: parentContext,
                  );
                  if (dialogContext.mounted) {
                    Navigator.of(dialogContext).pop();
                  }
                },
                onCancel: () {
                  if (dialogContext.mounted) {
                    Navigator.of(dialogContext).pop();
                  }
                },
              ),
            ),
          ),
        );
      },
    );
  }

  static void _showOverlayPicker({
    required BuildContext anchorContext,
    required BuildContext parentContext,
    required DateTime initialDate,
    required ValueNotifier selectMonth,
    required TopPerformersCubit topPerformersCubit,
  }) {
    OverlayEntry? entry;

    entry = OverlayEntry(
      builder: (context) => _ResponsiveCalendarOverlay(
        anchorContext: anchorContext,
        parentContext: parentContext,
        initialDate: initialDate,
        selectMonth: selectMonth,
        topPerformersCubit: topPerformersCubit,
        onClose: () => entry?.remove(),
      ),
    );

    final overlay = Overlay.of(parentContext);
    overlay.insert(entry);
  }

  static Future _handleDateSelection({
    required DateTime date,
    required ValueNotifier selectMonth,
    required TopPerformersCubit topPerformersCubit,
    required BuildContext parentContext,
  }) async {
    final formattedDate = DateFormat('yyyy-MM').format(date);
    selectMonth.value = formattedDate;

    if (!parentContext.mounted) return;

    final userRole = parentContext.read<UserCubit>().state.user?.role;

    if (userRole == UserRole.agent || userRole == UserRole.brokerage) {
      await topPerformersCubit.getAgentTopPerformers(formattedDate);
    } else if (userRole == UserRole.admin ||
        userRole == UserRole.platformOwner) {
      await topPerformersCubit.getBrokerageTopPerformers(formattedDate);
      await topPerformersCubit.getAgentTopPerformers(formattedDate);
    }
  }
}

class _ResponsiveCalendarOverlay extends StatefulWidget {
  final BuildContext anchorContext;
  final BuildContext parentContext;
  final DateTime initialDate;
  final ValueNotifier selectMonth;
  final TopPerformersCubit topPerformersCubit;
  final VoidCallback onClose;

  const _ResponsiveCalendarOverlay({
    required this.anchorContext,
    required this.parentContext,
    required this.initialDate,
    required this.selectMonth,
    required this.topPerformersCubit,
    required this.onClose,
  });

  @override
  State<_ResponsiveCalendarOverlay> createState() =>
      _ResponsiveCalendarOverlayState();
}

class _ResponsiveCalendarOverlayState extends State<_ResponsiveCalendarOverlay>
    with WidgetsBindingObserver {
  PickerPosition? _currentPosition;
  double? _pickerWidth;
  double? _pickerHeight;
  Size? _lastScreenSize;
  Offset? _initialAnchorOffset;
  double? _initialScreenWidth;
  double? _baseTopPosition;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updatePosition();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _resetPositionTracking();
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    if (mounted) {
      _updatePosition();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _updatePosition();
      }
    });
  }

  void _updatePosition() {
    if (!mounted) return;

    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final screenHeight = mediaQuery.size.height;

    final padding = mediaQuery.padding.left > 0
        ? mediaQuery.padding.left
        : 16.0;

    final pickerWidth = (screenWidth * 0.25).clamp(200.0, 350.0);
    final pickerHeight = (screenHeight * 0.5).clamp(350.0, 400.0);

    final currentScreenSize = Size(screenWidth, screenHeight);

    RenderBox? renderBox;
    try {
      renderBox = widget.anchorContext.findRenderObject() as RenderBox?;
    } catch (e) {
      _setDefaultPosition(
        screenWidth,
        screenHeight,
        pickerWidth,
        pickerHeight,
        padding,
      );
      return;
    }

    if (renderBox == null || !renderBox.hasSize) {
      _setDefaultPosition(
        screenWidth,
        screenHeight,
        pickerWidth,
        pickerHeight,
        padding,
      );
      return;
    }

    final Offset offset = renderBox.localToGlobal(Offset.zero);
    final Size size = renderBox.size;

    // Store initial values on first calculation
    if (_initialAnchorOffset == null || _initialScreenWidth == null) {
      _initialAnchorOffset = offset;
      _initialScreenWidth = screenWidth;
      _baseTopPosition = offset.dy + size.height + 8.0;
    }

    // Check if anchor element has wrapped to a new line
    final bool hasWrapped = _hasAnchorWrapped(offset);
    if (hasWrapped) {
      // Option 1: Close the calendar (current behavior)
      // widget.onClose();
      // return;

      // Option 2: Reposition above the anchor (alternative behavior)
      // Reset tracking and position above
      _resetPositionTracking();
      _initialAnchorOffset = offset;
      _initialScreenWidth = screenWidth;
      _baseTopPosition = offset.dy - pickerHeight - 8.0; // Position above
    }

    final newPosition = _calculateOptimalPosition(
      anchorOffset: offset,
      anchorSize: size,
      screenWidth: screenWidth,
      screenHeight: screenHeight,
      pickerWidth: pickerWidth,
      pickerHeight: pickerHeight,
      padding: padding,
      anchorGap: 8.0,
    );

    if (mounted) {
      setState(() {
        _currentPosition = newPosition;
        _pickerWidth = pickerWidth;
        _pickerHeight = pickerHeight;
        _lastScreenSize = currentScreenSize;
      });
    }
  }

  bool _hasAnchorWrapped(Offset currentOffset) {
    if (_initialAnchorOffset == null) return false;

    // Check if the anchor has moved significantly vertically (indicating a wrap)
    final verticalDifference = (currentOffset.dy - _initialAnchorOffset!.dy).abs();

    // If vertical difference is more than 30 pixels, consider it wrapped
    return verticalDifference > 30.0;
  }

  void _resetPositionTracking() {
    _initialAnchorOffset = null;
    _initialScreenWidth = null;
    _baseTopPosition = null;
  }

  void _setDefaultPosition(
    double screenWidth,
    double screenHeight,
    double pickerWidth,
    double pickerHeight,
    double padding,
  ) {
    double left;
    if (screenWidth - pickerWidth - padding >= padding) {
      left = screenWidth - pickerWidth - padding;
    } else {
      left = (screenWidth - pickerWidth) / 2;
      left = left.clamp(padding, screenWidth - pickerWidth - padding);
    }
    final top = (screenHeight - pickerHeight) / 2;
    if (mounted) {
      setState(() {
        _currentPosition = PickerPosition(left: left, top: top);
        _pickerWidth = pickerWidth;
        _pickerHeight = pickerHeight;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (_currentPosition == null) {
          return const SizedBox.shrink();
        }
        return NotificationListener<SizeChangedLayoutNotification>(
          onNotification: (notification) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                _updatePosition();
              }
            });
            return true;
          },
          child: MediaQuery(
            data: MediaQuery.of(context),
            child: LayoutBuilder(
              builder: (context, constraints) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (mounted) {
                    final currentMediaQuery = MediaQuery.of(context);
                    if (_lastScreenSize != null &&
                        (_lastScreenSize!.width !=
                                currentMediaQuery.size.width ||
                            _lastScreenSize!.height !=
                                currentMediaQuery.size.height)) {
                      _updatePosition();
                    }
                  }
                });

                return Material(
                  key: UniqueKey(),
                  type: MaterialType.transparency,
                  child: Stack(
                    children: [
                      Positioned.fill(
                        child: GestureDetector(
                          onTap: widget.onClose,
                          child: Container(
                            color: Colors.black.withValues(alpha: 0.3),
                          ),
                        ),
                      ),
                      AnimatedPositioned(
                        duration: const Duration(milliseconds: 200),
                        curve: Curves.easeInOut,
                        left: _currentPosition!.left,
                        top: _currentPosition!.top,
                        child: Material(
                          elevation: 8.0,
                          borderRadius: BorderRadius.circular(8.0),
                          child: SizedBox(
                            width: _pickerWidth,
                            height: _pickerHeight,
                            child: MonthYearPicker(
                              anchorKey: GlobalKey(),
                              initialDate: widget.initialDate,
                              onDateSelected: (DateTime date) async {
                                await CalendarPickerService._handleDateSelection(
                                  date: date,
                                  selectMonth: widget.selectMonth,
                                  topPerformersCubit: widget.topPerformersCubit,
                                  parentContext: widget.parentContext,
                                );
                                widget.onClose();
                              },
                              onCancel: widget.onClose,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  PickerPosition _calculateOptimalPosition({
    required Offset anchorOffset,
    required Size anchorSize,
    required double screenWidth,
    required double screenHeight,
    required double pickerWidth,
    required double pickerHeight,
    required double padding,
    required double anchorGap,
  }) {
    // IMPROVED TOP POSITIONING: Keep the same row position
    double top;
    if (_baseTopPosition != null) {
      // Use the base top position to maintain same row
      top = _baseTopPosition!;
    } else {
      // Fallback to anchor-based positioning
      top = anchorOffset.dy + anchorSize.height + anchorGap;
    }

    // If picker would overflow bottom, move it above the anchor
    final double bottomOverflow = (top + pickerHeight) - (screenHeight - padding);
    if (bottomOverflow > 0) {
      final double abovePosition = anchorOffset.dy - pickerHeight - anchorGap;
      if (abovePosition >= padding) {
        top = abovePosition;
      } else {
        top = (screenHeight - pickerHeight - padding).clamp(
          padding,
          screenHeight - pickerHeight - padding,
        );
      }
    }

    // Clamp to avoid going off screen
    top = top.clamp(padding, screenHeight - pickerHeight - padding);

    // IMPROVED LEFT POSITIONING: Responsive to width changes
    final double minLeft = padding;
    final double maxLeft = screenWidth - pickerWidth - padding;

    double left;
    if (maxLeft < minLeft) {
      left = (screenWidth - pickerWidth) / 2;
    } else {
      // Calculate width change ratio for responsive positioning
      double widthRatio = 1.0;
      if (_initialScreenWidth != null && _initialScreenWidth! > 0) {
        widthRatio = screenWidth / _initialScreenWidth!;
      }

      // Start with anchor-aligned positioning
      final double anchorRightEdge = anchorOffset.dx + anchorSize.width;
      final double preferredLeft = anchorRightEdge - pickerWidth;

      if (preferredLeft >= minLeft && preferredLeft <= maxLeft) {
        left = preferredLeft;
      } else {
        // Responsive positioning based on screen width changes
        if (widthRatio < 1.0) {
          // Screen width decreased - move calendar left
          left = minLeft + (maxLeft - minLeft) * 0.1; // 10% from left edge
        } else if (widthRatio > 1.0) {
          // Screen width increased - move calendar right
          left = maxLeft - (maxLeft - minLeft) * 0.1; // 10% from right edge
        } else {
          // No width change - use anchor-based positioning
          left = anchorOffset.dx.clamp(minLeft, maxLeft);
        }
      }
    }

    left = left.clamp(minLeft, maxLeft);

    return PickerPosition(left: left, top: top);
  }
}

class PickerPosition {
  final double left;
  final double top;
  const PickerPosition({required this.left, required this.top});
}
