part of 'agent_cubit.dart';

@immutable
sealed class AgentState {}

final class AgentInitial extends AgentState {}

final class AgentLoading extends AgentState {}

final class AgentLoaded extends AgentState {
  final List<AgentModel> agents;
  final int totalCount;
  final int currentPage;
  final bool hasMore;

  AgentLoaded({
    required this.agents,
    required this.totalCount,
    required this.currentPage,
    this.hasMore = false,
  });
}

final class AgentError extends AgentState {
  final String message;
  final int? statusCode;

  AgentError({required this.message, this.statusCode});
}

/// Agent Registartion
final class AgentRegistrationLoading extends AgentState {}

final class AgentCreated extends AgentState {
  AgentCreated();
}

final class AgentRegistrationError extends AgentState {
  final String message;
  final int? statusCode;

  AgentRegistrationError({required this.message, this.statusCode});
}
