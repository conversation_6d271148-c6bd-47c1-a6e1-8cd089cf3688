import 'dart:math';

import 'package:bloc/bloc.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:meta/meta.dart';
import '../../../core/config/app_strings.dart' as AppStrings;
import '../../../core/network/api_consts.dart';
import '../../../core/services/exceptions.dart';
import '../../../domain/models/agent_model.dart';
import '../../../domain/repository/agent_repository.dart';

part 'agent_state.dart';

class AgentCubit extends Cubit<AgentState> {
  final AgentRepository _agentRepository;

  AgentCubit(this._agentRepository) : super(AgentInitial());

  /// Get list of agents with POST request
  Future<void> getAgents({
    int page = 0,
    int size = 20,
    String sortBy = "id",
    String sortDirection = "ASC",
    String searchString = "",
    DateTime? joiningDate,
    required String userId,
    bool loadMore = false,
  }) async {
    if (!loadMore) {
      emit(AgentLoading());
    }

    try {
      // Build request body in cubit
      final Map<String, dynamic> requestBody = {
        'page': page,
        'size': size,
        'sortBy': sortBy,
        'sortDirection': sortDirection,
        'searchString': searchString,
        'joiningDate': joiningDate?.toIso8601String(),
        'userId': userId,
      };

      final response = await _agentRepository.getAgents(requestBody);

      emit(
        AgentLoaded(
          agents: response.content,
          totalCount: response.totalElements,
          currentPage: response.number,
          hasMore: !response.last,
        ),
      );
    } on ApiException catch (e) {
      emit(AgentError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        AgentError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  /// Get agent details by ID
  Future<void> getAgentById(String agentId) async {
    emit(AgentLoading());

    try {
      final agent = await _agentRepository.getAgentById(agentId);
      emit(
        AgentLoaded(
          agents: [agent],
          totalCount: 1,
          currentPage: 0,
          hasMore: false,
        ),
      );
    } on ApiException catch (e) {
      emit(AgentError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        AgentError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  /// Clear current state
  void clearAgents() {
    emit(AgentInitial());
  }

  /// Create a new agent
  Future<void> registerAgent(
    Map<String, dynamic> requestBody,
    PlatformFile? file,
  ) async {
    emit(AgentRegistrationLoading());
    try {
      final response = await _agentRepository.createAgent(requestBody);
      if (response != false) {
        final data = response['data'];
        String? userId = data['userID'];

        await _uploadAgentFile(file, userId);
      } else {
        emit(AgentRegistrationError(message: 'Failed to create agent'));
      }
    } on ApiException catch (e) {
      emit(
        AgentRegistrationError(message: e.message, statusCode: e.statusCode),
      );
    } catch (e) {
      emit(
        AgentRegistrationError(
          message: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }

  /// Upload agent file
  Future<void> _uploadAgentFile(PlatformFile? file, String? userId) async {
    if (file == null || userId == null) {
      emit(
        AgentRegistrationError(
          message: '${AppStrings.invalidFile} or userId is null',
        ),
      );
      return;
    }

    final isValidFile = kIsWeb ? file.bytes != null : file.path != null;
    if (!isValidFile) {
      emit(AgentRegistrationError(message: AppStrings.invalidFile));
      return;
    }

    try {
      final uploadFilePayload = {
        "userId": userId,
        "categoryType": APIConsts.agentCategoryType,
        "documentType": APIConsts.agentDocType,
        "file": file,
      };

      final success = await _agentRepository.uploadAgentFile(uploadFilePayload);

      if (success) {
        emit(AgentCreated());
      } else {
        emit(AgentRegistrationError(message: 'Failed to upload agent file'));
      }
    } on ApiException catch (e) {
      emit(
        AgentRegistrationError(message: e.message, statusCode: e.statusCode),
      );
    } catch (e) {
      emit(AgentRegistrationError(message: 'An unexpected error occurred: $e'));
    }
  }
}
